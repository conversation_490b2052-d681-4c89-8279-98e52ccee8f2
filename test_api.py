#!/usr/bin/env python3
"""
Test script for the FastAPI migration.
Tests all endpoints to ensure they work correctly.
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000/api"

def test_health_endpoint():
    """Test the health check endpoint."""
    print("Testing /health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        assert response.status_code == 200
        assert response.json()["status"] == "API is running"
        print("✅ Health endpoint test passed!\n")
        return True
    except Exception as e:
        print(f"❌ Health endpoint test failed: {e}\n")
        return False

def test_generate_tags_test_endpoint():
    """Test the generate_tags_test endpoint."""
    print("Testing /generate_tags_test endpoint...")
    try:
        test_data = {
            "items": [
                {
                    "index": 1,
                    "key": "test_article_1",
                    "title": "Test Article Title",
                    "abstract": "This is a test abstract for testing purposes."
                }
            ]
        }
        
        response = requests.post(f"{BASE_URL}/generate_tags_test", json=test_data)
        print(f"Status Code: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        
        assert response.status_code == 200
        assert len(result) == 1
        assert result[0]["index"] == 1
        assert result[0]["key"] == "test_article_1"
        assert "tags" in result[0]
        assert "matched_tags" in result[0]["tags"]
        print("✅ Generate tags test endpoint test passed!\n")
        return True
    except Exception as e:
        print(f"❌ Generate tags test endpoint test failed: {e}\n")
        return False

def test_tags_all_endpoint():
    """Test the /tags/all endpoint."""
    print("Testing /tags/all endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/tags/all")
        print(f"Status Code: {response.status_code}")
        result = response.json()
        print(f"Response type: {type(result)}")
        print(f"Response length: {len(result) if isinstance(result, list) else 'Not a list'}")
        
        assert response.status_code == 200
        assert isinstance(result, list)
        print("✅ Tags all endpoint test passed!\n")
        return True
    except Exception as e:
        print(f"❌ Tags all endpoint test failed: {e}\n")
        return False

def test_generate_tags_endpoint():
    """Test the main /generate_tags endpoint."""
    print("Testing /generate_tags endpoint...")
    try:
        test_data = {
            "items": [
                {
                    "index": 1,
                    "key": "test_article_1",
                    "title": "Machine Learning in Healthcare",
                    "abstract": "This paper explores the application of machine learning algorithms in healthcare diagnostics and treatment planning."
                }
            ],
            "model": "pytextrank"  # Use TextRank pipeline for faster testing
        }
        
        response = requests.post(f"{BASE_URL}/generate_tags", json=test_data)
        print(f"Status Code: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        
        assert response.status_code == 200
        assert len(result) == 1
        assert result[0]["index"] == 1
        assert result[0]["key"] == "test_article_1"
        assert "tags" in result[0]
        print("✅ Generate tags endpoint test passed!\n")
        return True
    except Exception as e:
        print(f"❌ Generate tags endpoint test failed: {e}\n")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting FastAPI endpoint tests...\n")
    
    # Wait for server to be ready
    print("Waiting for server to be ready...")
    max_retries = 30
    for i in range(max_retries):
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Server is ready!\n")
                break
        except:
            pass
        
        if i == max_retries - 1:
            print("❌ Server is not responding. Please make sure the FastAPI server is running.\n")
            return
        
        print(f"Waiting... ({i+1}/{max_retries})")
        time.sleep(2)
    
    # Run tests
    tests = [
        test_health_endpoint,
        test_generate_tags_test_endpoint,
        test_tags_all_endpoint,
        test_generate_tags_endpoint,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! FastAPI migration is successful!")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
