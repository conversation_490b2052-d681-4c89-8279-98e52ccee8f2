#!/usr/bin/env python3
"""
Test script to verify the filter implementation works with the attach_label.py patterns.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from core.embeddings_management import Embedding<PERSON><PERSON><PERSON>

def test_attach_label_integration():
    """Test the filter patterns used in attach_label.py."""
    
    print("Testing attach_label.py Integration")
    print("=" * 40)
    
    # Initialize the EmbeddingManager
    embedding_manager = EmbeddingManager(embedding_model="all-MiniLM-L6-v2")
    
    collection_name = "test_attach_label_collection"
    
    # Test data similar to what would be in a label pool
    test_data = [
        {"label_id": 1, "name": "Machine Learning", "category": "Concept"},
        {"label_id": 2, "name": "Deep Learning", "category": "Concept"},
        {"label_id": 3, "name": "Neural Networks", "category": "Concept"},
        {"label_id": 4, "name": "<PERSON>", "category": "Persons"},
        {"label_id": 5, "name": "<PERSON>", "category": "Persons"},
        {"label_id": 6, "name": "Harvard University", "category": "Institutions"},
        {"label_id": 7, "name": "MIT", "category": "Institutions"},
        {"label_id": 8, "name": "New York", "category": "Times and Places"},
        {"label_id": 9, "name": "World War II", "category": "Times and Places"},
    ]
    
    try:
        # Clean up any existing collection
        try:
            embedding_manager.delete_collection(collection_name)
        except:
            pass
        
        # Create collection and add test data
        print("1. Setting up test collection...")
        result = embedding_manager.create_collection(collection_name)
        if result == "success":
            embedding_manager.upsert_data(collection_name, test_data)
            print("✓ Collection created and data added")
        else:
            print(f"✗ Failed to create collection: {result}")
            return
        
        # Simulate the category_map and filter patterns from attach_label.py
        category_map = {
            "keywords": ["Concept"], 
            "persons_organizations": ["Persons", "Institutions"], 
            "times_places": ["Times and Places"]
        }
        
        # Simulate keywords extracted from text
        keywords_dict_from_spacy = {
            "keywords": ["learning", "intelligence", "neural"],
            "persons_organizations": ["smith", "university"],
            "times_places": ["york", "war"]
        }
        
        print("\n2. Testing attach_label.py filter patterns...")
        
        # Test the exact pattern used in attach_label.py
        matched_labels_dict = {}
        for keywords_type, keywords_list in keywords_dict_from_spacy.items():
            if keywords_list:  # Only call search() if keywords_list is not empty
                filter_str = f'category in {category_map[keywords_type]}'
                print(f"   Testing {keywords_type} with filter: {filter_str}")
                
                results = embedding_manager.search(
                    collection_name=collection_name,
                    query_texts=keywords_list,
                    top_k=2,
                    simple_output=True,
                    filter=filter_str
                )
                matched_labels_dict[keywords_type] = results
                print(f"   Results: {results}")
            else:
                matched_labels_dict[keywords_type] = []
        
        print(f"\n3. Final matched_labels_dict: {matched_labels_dict}")
        
        # Verify we got reasonable results for each category
        success = True
        if not matched_labels_dict.get("keywords"):
            print("✗ No concept keywords matched")
            success = False
        if not matched_labels_dict.get("persons_organizations"):
            print("✗ No persons/organizations matched")
            success = False
        if not matched_labels_dict.get("times_places"):
            print("✗ No times/places matched")
            success = False
        
        if success:
            print("✓ All category filters worked correctly!")
        else:
            print("✗ Some category filters failed")
        
        # Test with search_by_category flag (conditional filter)
        print("\n4. Testing conditional filter (search_by_category=False)...")
        search_by_category = False
        results = embedding_manager.search(
            collection_name=collection_name,
            query_texts=["learning"],
            top_k=2,
            simple_output=True,
            filter=f'category in {category_map["keywords"]}' if search_by_category else None
        )
        print(f"   Results with search_by_category=False: {results}")
        
        search_by_category = True
        results = embedding_manager.search(
            collection_name=collection_name,
            query_texts=["learning"],
            top_k=2,
            simple_output=True,
            filter=f'category in {category_map["keywords"]}' if search_by_category else None
        )
        print(f"   Results with search_by_category=True: {results}")
        
        print("\n✓ Integration test completed successfully!")
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        try:
            embedding_manager.delete_collection(collection_name)
            print("\n✓ Test collection cleaned up")
        except:
            pass

if __name__ == "__main__":
    test_attach_label_integration()
