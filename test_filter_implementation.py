#!/usr/bin/env python3
"""
Test script to verify the ChromaDB filter implementation for both equality and 'IN' operators.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from core.embeddings_management import EmbeddingManager

def test_filter_implementation():
    """Test both equality and 'IN' operator filters."""
    
    print("Testing ChromaDB Filter Implementation")
    print("=" * 50)
    
    # Initialize the EmbeddingManager (will use ChromaDB on Windows)
    embedding_manager = EmbeddingManager(embedding_model="all-MiniLM-L6-v2")
    
    collection_name = "test_filter_collection"
    
    # Test data with different categories
    test_data = [
        {"label_id": 1, "name": "Machine Learning", "category": "Concept"},
        {"label_id": 2, "name": "Artificial Intelligence", "category": "Concept"},
        {"label_id": 3, "name": "<PERSON>", "category": "Persons"},
        {"label_id": 4, "name": "Harvard University", "category": "Institutions"},
        {"label_id": 5, "name": "New York", "category": "Times and Places"},
        {"label_id": 6, "name": "World War II", "category": "Times and Places"},
        {"label_id": 7, "name": "Data Science", "category": "Concept"},
        {"label_id": 8, "name": "Jane Doe", "category": "Persons"},
    ]
    
    try:
        # Clean up any existing collection
        try:
            embedding_manager.delete_collection(collection_name)
        except:
            pass
        
        # Create collection and add test data
        print("1. Creating collection and adding test data...")
        result = embedding_manager.create_collection(collection_name)
        if result == "success":
            embedding_manager.upsert_data(collection_name, test_data)
            print("✓ Collection created and data added successfully")
        else:
            print(f"✗ Failed to create collection: {result}")
            return
        
        # Test 1: Simple equality filter
        print("\n2. Testing equality filter: category == \"Concept\"")
        results = embedding_manager.search(
            collection_name=collection_name,
            query_texts=["learning", "intelligence"],
            top_k=5,
            simple_output=True,
            filter='category == "Concept"'
        )
        print(f"Results: {results}")
        expected_concepts = {"Machine Learning", "Artificial Intelligence", "Data Science"}
        if any(result in expected_concepts for result in results):
            print("✓ Equality filter works correctly")
        else:
            print("✗ Equality filter failed")
        
        # Test 2: Single value 'IN' filter
        print("\n3. Testing single value 'IN' filter: category in [\"Concept\"]")
        results = embedding_manager.search(
            collection_name=collection_name,
            query_texts=["learning", "science"],
            top_k=5,
            simple_output=True,
            filter='category in ["Concept"]'
        )
        print(f"Results: {results}")
        if any(result in expected_concepts for result in results):
            print("✓ Single value 'IN' filter works correctly")
        else:
            print("✗ Single value 'IN' filter failed")
        
        # Test 3: Multiple values 'IN' filter
        print("\n4. Testing multiple values 'IN' filter: category in [\"Persons\", \"Institutions\"]")
        results = embedding_manager.search(
            collection_name=collection_name,
            query_texts=["person", "university"],
            top_k=5,
            simple_output=True,
            filter='category in ["Persons", "Institutions"]'
        )
        print(f"Results: {results}")
        expected_persons_institutions = {"John Smith", "Harvard University", "Jane Doe"}
        if any(result in expected_persons_institutions for result in results):
            print("✓ Multiple values 'IN' filter works correctly")
        else:
            print("✗ Multiple values 'IN' filter failed")
        
        # Test 4: Test query method with 'IN' filter
        print("\n5. Testing query method with 'IN' filter: category in [\"Times and Places\"]")
        results = embedding_manager.query(
            collection_name=collection_name,
            filter='category in ["Times and Places"]',
            output_fields=["name", "category"]
        )
        print(f"Query results: {results}")
        expected_times_places = {"New York", "World War II"}
        result_names = {result.get('name', '') for result in results}
        if result_names.intersection(expected_times_places):
            print("✓ Query method with 'IN' filter works correctly")
        else:
            print("✗ Query method with 'IN' filter failed")
        
        # Test 5: Test with category_map pattern (like in attach_label.py)
        print("\n6. Testing with category_map pattern...")
        category_map = {
            "keywords": ["Concept"], 
            "persons_organizations": ["Persons", "Institutions"], 
            "times_places": ["Times and Places"]
        }
        
        for keywords_type, categories in category_map.items():
            filter_str = f'category in {categories}'
            print(f"   Testing filter: {filter_str}")
            results = embedding_manager.search(
                collection_name=collection_name,
                query_texts=["test", "query"],
                top_k=3,
                simple_output=True,
                filter=filter_str
            )
            print(f"   Results for {keywords_type}: {results}")
        
        print("\n✓ All tests completed successfully!")
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        try:
            embedding_manager.delete_collection(collection_name)
            print("\n✓ Test collection cleaned up")
        except:
            pass

if __name__ == "__main__":
    test_filter_implementation()
